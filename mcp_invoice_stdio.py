#!/usr/bin/env python3
"""
发票识别LLM MCP Server (stdio版本)
使用 FastMCP 和 stdio 协议
"""

import base64
import tempfile
import os
import requests
from fastmcp import FastMCP
from invoice_core_llm import inference

# 创建 FastMCP 应用
mcp = FastMCP("发票识别LLM")

def download_image(image_url):
    """
    从 URL 下载图像并保存到临时文件
    
    Args:
        image_url: 图像的 URL
    
    Returns:
        临时文件路径
    """
    # 创建临时文件
    temp_file = tempfile.NamedTemporaryFile(suffix='.jpg', delete=False)
    temp_file_path = temp_file.name
    temp_file.close()
    
    try:
        # 下载图像
        response = requests.get(image_url, timeout=30)
        response.raise_for_status()
        
        # 保存到临时文件
        with open(temp_file_path, 'wb') as f:
            f.write(response.content)
            
        return temp_file_path
    except Exception as e:
        # 清理临时文件
        if os.path.exists(temp_file_path):
            os.unlink(temp_file_path)
        raise e

@mcp.tool()
def recognize_single_invoice(image_url: str = None, image_data: str = None, session_id: str = None,
                           api_key: str = None, base_url: str = None, model: str = None) -> dict:
    """
    识别单张发票图像

    Args:
        image_data: base64 编码的图像数据（推荐使用，避免网络依赖）
        image_url: 图像的 URL（备用选项，需要网络访问）
        session_id: 会话ID，用于获取环境变量中的配置（向后兼容）
        api_key: OpenAI API密钥
        base_url: OpenAI API基础URL
        model: OpenAI模型名称

    Returns:
        发票识别结果

    Note:
        优先使用 image_data 参数以避免网络依赖问题，特别是在公网环境中。
        image_url 作为备用选项，但可能在某些网络环境下无法访问。
    """
    try:
        # 确定图像源 - 优先使用 image_data，避免网络依赖
        if image_data and image_data != "base64_encoded_image_data":
            # 解码 base64 图像数据
            try:
                # 清理 base64 字符串，移除可能的前缀和空白字符
                clean_image_data = image_data.strip()

                # 如果包含 data URL 前缀，提取实际的 base64 数据
                if clean_image_data.startswith('data:'):
                    # 格式: data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEA...
                    if ',' in clean_image_data:
                        clean_image_data = clean_image_data.split(',', 1)[1]

                # 移除所有非 base64 字符（只保留 A-Z, a-z, 0-9, +, /, =）
                import re
                clean_image_data = re.sub(r'[^A-Za-z0-9+/=]', '', clean_image_data)

                # 确保 base64 字符串长度是4的倍数
                missing_padding = len(clean_image_data) % 4
                if missing_padding:
                    clean_image_data += '=' * (4 - missing_padding)

                # 验证是否为有效的 base64 字符串
                if not clean_image_data or len(clean_image_data) < 4:
                    raise ValueError("base64 数据太短或为空")

                image_bytes = base64.b64decode(clean_image_data)
            except Exception as e:
                return {
                    "success": False,
                    "message": f"Base64 解码失败: {str(e)}。请确保提供有效的 base64 编码图片数据。"
                }

            # 创建临时文件
            with tempfile.NamedTemporaryFile(suffix='.jpg', delete=False) as tmp_file:
                tmp_file.write(image_bytes)
                tmp_file_path = tmp_file.name
        elif image_url:
            # 从 URL 下载图像（备用方案）
            tmp_file_path = download_image(image_url)
        else:
            return {
                "success": False,
                "message": "请提供有效的 image_data 或 image_url 参数"
            }
        
        try:
            # 获取OpenAI配置：优先使用直接传递的参数，其次从环境变量中获取（向后兼容）
            if api_key and base_url and model:
                # 使用直接传递的参数
                pass
            elif session_id:
                # 从环境变量中获取OpenAI配置（向后兼容）
                api_key = os.environ.get(f"OPENAI_API_KEY_{session_id}")
                base_url = os.environ.get(f"OPENAI_BASE_URL_{session_id}")
                model = os.environ.get(f"OPENAI_MODEL_{session_id}")
                
                if not api_key or not base_url or not model:
                    return {
                        "success": False,
                        "message": f"Session {session_id} 缺少必要的OpenAI配置信息"
                    }
            else:
                return {
                    "success": False,
                    "message": "缺少OpenAI配置信息，请提供api_key、base_url和model参数，或提供session_id"
                }
            
            # 调用使用OpenAI API的推理函数
            im_show, invoice_fields = inference(tmp_file_path, 'ch', api_key, base_url, model)
            
            # 返回结果
            return {
                "success": True,
                "invoice_fields": invoice_fields,
                "message": "发票识别完成"
            }
        finally:
            # 清理临时文件
            if os.path.exists(tmp_file_path):
                os.unlink(tmp_file_path)
            
    except Exception as e:
        return {
            "success": False,
            "message": f"发票识别失败: {str(e)}"
        }

@mcp.tool()
def recognize_multiple_invoices(image_list: list, session_id: str = None,
                              api_key: str = None, base_url: str = None, model: str = None) -> dict:
    """
    识别多张发票图像
    
    Args:
        image_list: base64 编码的图像数据列表或 URL 列表
        session_id: 会话ID，用于获取环境变量中的配置（向后兼容）
        api_key: OpenAI API密钥
        base_url: OpenAI API基础URL
        model: OpenAI模型名称
    
    Returns:
        多张发票识别结果
    """
    results = []
    
    # 获取OpenAI配置：优先使用直接传递的参数，其次从环境变量中获取（向后兼容）
    if api_key and base_url and model:
        # 使用直接传递的参数
        pass
    elif session_id:
        # 从环境变量中获取OpenAI配置（向后兼容）
        api_key = os.environ.get(f"OPENAI_API_KEY_{session_id}")
        base_url = os.environ.get(f"OPENAI_BASE_URL_{session_id}")
        model = os.environ.get(f"OPENAI_MODEL_{session_id}")
        
        if not api_key or not base_url or not model:
            return {
                "success": False,
                "message": f"Session {session_id} 缺少必要的OpenAI配置信息"
            }
    else:
        return {
            "success": False,
            "message": "缺少OpenAI配置信息，请提供api_key、base_url和model参数，或提供session_id"
        }
    
    for i, image_item in enumerate(image_list):
        try:
            # 处理图像数据或 URL - 优先处理 base64 数据
            if isinstance(image_item, str):
                # 首先尝试作为 base64 数据处理
                try:
                    if not image_item.startswith('http'):
                        # 假设是 base64 数据，进行清理和修复
                        clean_image_data = image_item.strip()

                        # 如果包含 data URL 前缀，提取实际的 base64 数据
                        if clean_image_data.startswith('data:'):
                            if ',' in clean_image_data:
                                clean_image_data = clean_image_data.split(',', 1)[1]

                        # 移除所有非 base64 字符
                        import re
                        clean_image_data = re.sub(r'[^A-Za-z0-9+/=]', '', clean_image_data)

                        # 确保 base64 字符串长度是4的倍数
                        missing_padding = len(clean_image_data) % 4
                        if missing_padding:
                            clean_image_data += '=' * (4 - missing_padding)

                        # 验证数据有效性
                        if not clean_image_data or len(clean_image_data) < 4:
                            raise ValueError("base64 数据太短或为空")

                        image_bytes = base64.b64decode(clean_image_data)

                        # 创建临时文件
                        with tempfile.NamedTemporaryFile(suffix='.jpg', delete=False) as tmp_file:
                            tmp_file.write(image_bytes)
                            tmp_file_path = tmp_file.name
                    else:
                        # 是 URL，作为备用方案下载
                        tmp_file_path = download_image(image_item)
                except Exception as e:
                    # base64 解码失败，尝试作为 URL 处理
                    if image_item.startswith('http'):
                        tmp_file_path = download_image(image_item)
                    else:
                        results.append({
                            "index": i,
                            "success": False,
                            "message": f"无法处理图像数据: {str(e)}"
                        })
                        continue
            else:
                results.append({
                    "index": i,
                    "success": False,
                    "message": "图像列表中的项目必须是字符串（base64 数据或 URL）"
                })
                continue
            
            try:
                # 调用使用OpenAI API的推理函数
                im_show, invoice_fields = inference(tmp_file_path, 'ch', api_key, base_url, model)
                
                # 添加结果
                results.append({
                    "index": i,
                    "success": True,
                    "invoice_fields": invoice_fields
                })
            finally:
                # 清理临时文件
                if os.path.exists(tmp_file_path):
                    os.unlink(tmp_file_path)
                
        except Exception as e:
            results.append({
                "index": i,
                "success": False,
                "message": f"第{i+1}张发票识别失败: {str(e)}"
            })
    
    return {
        "success": True,
        "results": results,
        "message": f"批量发票识别完成，共处理{len(image_list)}张发票"
    }

@mcp.tool()
def get_invoice_template_info(session_id: str = None) -> dict:
    """
    获取支持的发票模板信息
    
    Args:
        session_id: 会话ID，用于获取环境变量中的配置
    
    Returns:
        支持的发票模板信息
    """
    return {
        "success": True,
        "supported_languages": ["ch", "en"],
        "supported_formats": ["jpg", "jpeg", "png", "bmp", "gif"],
        "max_file_size": "10MB",
        "supported_invoice_types": [
            "增值税专用发票",
            "增值税普通发票", 
            "机动车销售统一发票",
            "二手车销售统一发票",
            "定额发票",
            "通用机打发票",
            "通用定额发票"
        ],
        "extractable_fields": [
            "发票代码", "发票号码", "开票日期", "校验码",
            "销售方名称", "销售方纳税人识别号", "销售方地址电话", "销售方开户行及账号",
            "购买方名称", "购买方纳税人识别号", "购买方地址电话", "购买方开户行及账号",
            "货物或应税劳务名称", "规格型号", "单位", "数量", "单价", "金额",
            "税率", "税额", "价税合计", "合计金额", "合计税额",
            "收款人", "复核", "开票人", "备注"
        ],
        "message": "发票识别LLM系统支持多种发票类型和字段提取"
    }

if __name__ == "__main__":
    print(f"启动发票识别LLM MCP Server")
    
    mcp.run(
        transport="stdio"  # 使用 stdio 传输协议
    )
