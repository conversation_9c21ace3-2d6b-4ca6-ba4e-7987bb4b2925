#!/usr/bin/env python3
"""
测试图片数据传输功能
验证 MCP server 能够正确处理 base64 编码的图片数据
"""

import base64
import tempfile
import os
from PIL import Image
import numpy as np

def create_test_image():
    """创建一个测试图片并返回其base64编码"""
    # 创建一个简单的测试图片
    img_array = np.zeros((100, 200, 3), dtype=np.uint8)
    img_array[:, :, 0] = 255  # 红色背景
    
    # 添加一些文字区域（白色矩形模拟文字）
    img_array[20:40, 20:180] = [255, 255, 255]  # 白色矩形
    img_array[60:80, 20:180] = [255, 255, 255]  # 白色矩形
    
    # 转换为PIL图片
    img = Image.fromarray(img_array)
    
    # 保存到临时文件
    with tempfile.NamedTemporaryFile(suffix='.jpg', delete=False) as tmp_file:
        img.save(tmp_file.name, 'JPEG')
        
        # 读取并编码为base64
        img_data = None
        with open(tmp_file.name, 'rb') as f:
            img_data = f.read()

        # 清理临时文件
        os.unlink(tmp_file.name)

        # 编码为base64
        img_base64 = base64.b64encode(img_data).decode('utf-8')
        return img_base64

def test_base64_decoding():
    """测试base64解码功能"""
    print("创建测试图片...")
    img_base64 = create_test_image()
    print(f"Base64数据长度: {len(img_base64)}")
    
    # 测试解码
    try:
        img_bytes = base64.b64decode(img_base64)
        print(f"解码后图片数据大小: {len(img_bytes)} bytes")
        
        # 保存解码后的图片进行验证
        with tempfile.NamedTemporaryFile(suffix='.jpg', delete=False) as tmp_file:
            tmp_file.write(img_bytes)
            print(f"测试图片已保存到: {tmp_file.name}")
            
            # 验证图片可以正常打开
            img = Image.open(tmp_file.name)
            print(f"图片尺寸: {img.size}")
            print(f"图片模式: {img.mode}")
            
            # 清理临时文件
            os.unlink(tmp_file.name)
            
        print("✅ Base64编码/解码测试通过")
        return True
        
    except Exception as e:
        print(f"❌ Base64编码/解码测试失败: {e}")
        return False

def simulate_mcp_server_processing():
    """模拟MCP server处理图片数据的流程"""
    print("\n模拟MCP server处理流程...")
    
    # 创建测试数据
    img_base64 = create_test_image()
    
    # 模拟MCP server的处理逻辑
    try:
        # 解码base64数据
        image_bytes = base64.b64decode(img_base64)
        
        # 创建临时文件
        with tempfile.NamedTemporaryFile(suffix='.jpg', delete=False) as tmp_file:
            tmp_file.write(image_bytes)
            tmp_file_path = tmp_file.name
        
        print(f"临时文件创建成功: {tmp_file_path}")
        
        # 验证文件可以被图片处理库读取
        img = Image.open(tmp_file_path)
        print(f"图片验证成功: {img.size}, {img.mode}")
        
        # 清理临时文件
        os.unlink(tmp_file_path)
        
        print("✅ MCP server处理流程模拟成功")
        return True
        
    except Exception as e:
        print(f"❌ MCP server处理流程模拟失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试图片数据传输功能...\n")
    
    # 测试1: Base64编码解码
    test1_passed = test_base64_decoding()
    
    # 测试2: 模拟MCP server处理
    test2_passed = simulate_mcp_server_processing()
    
    # 总结
    print(f"\n测试结果:")
    print(f"Base64编码/解码测试: {'✅ 通过' if test1_passed else '❌ 失败'}")
    print(f"MCP server处理模拟: {'✅ 通过' if test2_passed else '❌ 失败'}")
    
    if test1_passed and test2_passed:
        print("\n🎉 所有测试通过！图片数据传输功能正常工作。")
        print("现在可以在公网环境中使用，无需依赖本地文件服务器。")
    else:
        print("\n⚠️  部分测试失败，请检查实现。")

if __name__ == "__main__":
    main()
