#!/usr/bin/env python3
"""
测试非 ASCII 字符处理修复
"""

import base64
import re

def test_ascii_character_filtering():
    """测试 ASCII 字符过滤功能"""
    print("测试 ASCII 字符过滤...")
    
    # 创建正常的 base64 数据
    test_data = b"Hello, World!"
    normal_base64 = base64.b64encode(test_data).decode('utf-8')
    print(f"正常 base64: {normal_base64}")
    
    # 添加一些非 ASCII 字符
    contaminated_base64 = normal_base64[:10] + "中文字符" + normal_base64[10:] + "🎉"
    print(f"污染的 base64: {contaminated_base64}")
    
    # 测试清理函数
    def clean_base64_string(data):
        """清理 base64 字符串"""
        # 移除所有非 base64 字符（只保留 A-Z, a-z, 0-9, +, /, =）
        clean_data = re.sub(r'[^A-Za-z0-9+/=]', '', data)
        
        # 确保长度是4的倍数
        missing_padding = len(clean_data) % 4
        if missing_padding:
            clean_data += '=' * (4 - missing_padding)
        
        return clean_data
    
    try:
        cleaned_base64 = clean_base64_string(contaminated_base64)
        print(f"清理后 base64: {cleaned_base64}")
        
        # 验证解码
        decoded_data = base64.b64decode(cleaned_base64)
        print(f"解码结果: {decoded_data}")
        
        if decoded_data == test_data:
            print("✅ ASCII 字符过滤测试通过")
            return True
        else:
            print("❌ 解码数据不匹配")
            return False
            
    except Exception as e:
        print(f"❌ ASCII 字符过滤测试失败: {e}")
        return False

def test_various_contaminations():
    """测试各种污染情况"""
    print("\n测试各种污染情况...")
    
    # 创建基础数据
    test_data = b"Test image data for contamination test"
    base_base64 = base64.b64encode(test_data).decode('utf-8')
    
    test_cases = [
        ("正常数据", base_base64),
        ("包含中文", base_base64[:10] + "测试" + base_base64[10:]),
        ("包含emoji", base_base64 + "🎉🚀"),
        ("包含换行", base_base64[:20] + "\n\r" + base_base64[20:]),
        ("包含空格", base_base64[:15] + "   " + base_base64[15:]),
        ("包含特殊符号", base_base64 + "@#$%^&*()"),
        ("混合污染", "前缀" + base_base64[:10] + "\n测试🎉" + base_base64[10:] + "后缀"),
    ]
    
    def robust_base64_decode(data):
        """健壮的 base64 解码"""
        try:
            # 清理数据
            clean_data = data.strip()
            
            # 处理 data URL
            if clean_data.startswith('data:'):
                if ',' in clean_data:
                    clean_data = clean_data.split(',', 1)[1]
            
            # 移除非 base64 字符
            clean_data = re.sub(r'[^A-Za-z0-9+/=]', '', clean_data)
            
            # 修复填充
            missing_padding = len(clean_data) % 4
            if missing_padding:
                clean_data += '=' * (4 - missing_padding)
            
            # 验证长度
            if not clean_data or len(clean_data) < 4:
                return None, "数据太短或为空"
            
            # 解码
            decoded = base64.b64decode(clean_data)
            return decoded, "成功"
            
        except Exception as e:
            return None, str(e)
    
    success_count = 0
    for name, test_case in test_cases:
        print(f"\n测试: {name}")
        print(f"  输入长度: {len(test_case)}")
        
        result, message = robust_base64_decode(test_case)
        if result is not None:
            if result == test_data:
                print(f"  ✅ 成功: 数据完全匹配")
                success_count += 1
            else:
                print(f"  ⚠️  部分成功: 解码成功但数据不匹配")
        else:
            print(f"  ❌ 失败: {message}")
    
    print(f"\n污染测试结果: {success_count}/{len(test_cases)} 个用例完全成功")
    return success_count >= len(test_cases) // 2  # 至少一半成功

def test_edge_cases_with_ascii():
    """测试包含 ASCII 问题的边界情况"""
    print("\n测试 ASCII 相关边界情况...")
    
    test_cases = [
        ("空字符串", ""),
        ("只有非ASCII", "中文测试🎉"),
        ("只有空格和换行", "   \n\r\t   "),
        ("有效base64+非ASCII", "SGVsbG8=" + "中文"),
        ("data URL + 非ASCII", "data:image/png;base64,SGVsbG8=" + "🎉"),
        ("非ASCII前缀", "前缀SGVsbG8="),
        ("混合字符", "SGV中sbG文8="),
    ]
    
    def safe_decode_with_ascii_handling(data):
        """安全解码，处理 ASCII 问题"""
        try:
            if not data or not data.strip():
                return None, "空数据"
            
            clean_data = data.strip()
            
            # 处理 data URL
            if clean_data.startswith('data:'):
                if ',' in clean_data:
                    clean_data = clean_data.split(',', 1)[1]
            
            # 移除非 base64 字符
            clean_data = re.sub(r'[^A-Za-z0-9+/=]', '', clean_data)
            
            if not clean_data:
                return None, "清理后无有效数据"
            
            # 修复填充
            missing_padding = len(clean_data) % 4
            if missing_padding:
                clean_data += '=' * (4 - missing_padding)
            
            if len(clean_data) < 4:
                return None, "数据太短"
            
            # 解码
            decoded = base64.b64decode(clean_data)
            return decoded, "成功"
            
        except Exception as e:
            return None, str(e)
    
    success_count = 0
    for name, test_case in test_cases:
        print(f"\n测试: {name}")
        print(f"  输入: '{test_case}'")
        
        result, message = safe_decode_with_ascii_handling(test_case)
        if result is not None:
            print(f"  ✅ 成功: {message}, 解码长度: {len(result)}")
            success_count += 1
        else:
            print(f"  ⚠️  处理: {message}")
    
    print(f"\nASCII 边界测试结果: {success_count}/{len(test_cases)} 个用例成功解码")
    return True  # 边界测试主要是验证不会崩溃

def main():
    """主测试函数"""
    print("开始测试 ASCII 字符处理修复...\n")
    
    test1 = test_ascii_character_filtering()
    test2 = test_various_contaminations()
    test3 = test_edge_cases_with_ascii()
    
    print(f"\n总体测试结果:")
    print(f"ASCII 字符过滤测试: {'✅ 通过' if test1 else '❌ 失败'}")
    print(f"污染情况测试: {'✅ 通过' if test2 else '❌ 失败'}")
    print(f"ASCII 边界测试: {'✅ 通过' if test3 else '❌ 失败'}")
    
    if test1 and test2 and test3:
        print("\n🎉 所有测试通过！ASCII 字符处理修复功能正常工作。")
        print("现在可以处理包含各种非 ASCII 字符的 base64 数据。")
    else:
        print("\n⚠️  部分测试失败，需要进一步检查。")

if __name__ == "__main__":
    main()
