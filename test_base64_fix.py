#!/usr/bin/env python3
"""
测试 base64 解码修复功能
"""

import base64
import tempfile
import os

def test_base64_padding_fix():
    """测试 base64 填充修复功能"""
    print("测试 base64 填充修复...")
    
    # 创建一个正常的 base64 字符串
    test_data = b"Hello, World! This is a test image data."
    normal_base64 = base64.b64encode(test_data).decode('utf-8')
    print(f"正常 base64: {normal_base64}")
    print(f"长度: {len(normal_base64)}")
    
    # 模拟可能出现的问题：长度不是4的倍数
    broken_base64 = normal_base64[:-1]  # 移除最后一个字符
    print(f"损坏 base64: {broken_base64}")
    print(f"长度: {len(broken_base64)}")
    print(f"长度 % 4 = {len(broken_base64) % 4}")
    
    # 测试修复逻辑
    def fix_base64_padding(data):
        """修复 base64 填充的函数"""
        clean_data = data.strip()
        
        # 如果包含 data URL 前缀，提取实际的 base64 数据
        if clean_data.startswith('data:'):
            if ',' in clean_data:
                clean_data = clean_data.split(',', 1)[1]
        
        # 确保 base64 字符串长度是4的倍数
        missing_padding = len(clean_data) % 4
        if missing_padding:
            clean_data += '=' * (4 - missing_padding)
        
        return clean_data
    
    # 测试修复
    try:
        fixed_base64 = fix_base64_padding(broken_base64)
        print(f"修复后 base64: {fixed_base64}")
        print(f"修复后长度: {len(fixed_base64)}")
        
        # 尝试解码
        decoded_data = base64.b64decode(fixed_base64)
        print(f"解码成功: {decoded_data}")
        print("✅ base64 填充修复测试通过")
        return True
        
    except Exception as e:
        print(f"❌ base64 填充修复测试失败: {e}")
        return False

def test_data_url_handling():
    """测试 data URL 处理"""
    print("\n测试 data URL 处理...")
    
    # 创建测试数据
    test_data = b"Test image data"
    base64_data = base64.b64encode(test_data).decode('utf-8')
    
    # 创建 data URL
    data_url = f"data:image/jpeg;base64,{base64_data}"
    print(f"Data URL: {data_url}")
    
    # 测试提取逻辑
    def extract_base64_from_data_url(data_url):
        """从 data URL 中提取 base64 数据"""
        clean_data = data_url.strip()
        
        if clean_data.startswith('data:'):
            if ',' in clean_data:
                clean_data = clean_data.split(',', 1)[1]
        
        return clean_data
    
    try:
        extracted_base64 = extract_base64_from_data_url(data_url)
        print(f"提取的 base64: {extracted_base64}")
        
        # 验证解码
        decoded_data = base64.b64decode(extracted_base64)
        print(f"解码结果: {decoded_data}")
        
        if decoded_data == test_data:
            print("✅ data URL 处理测试通过")
            return True
        else:
            print("❌ 解码数据不匹配")
            return False
            
    except Exception as e:
        print(f"❌ data URL 处理测试失败: {e}")
        return False

def test_edge_cases():
    """测试边界情况"""
    print("\n测试边界情况...")
    
    test_cases = [
        "",  # 空字符串
        "   ",  # 只有空格
        "invalid_base64_!@#",  # 无效字符
        "SGVsbG8=",  # 正常 base64
        "SGVsbG8",   # 缺少填充
        "data:image/png;base64,SGVsbG8=",  # data URL
        "data:image/jpeg;base64,SGVsbG8",   # data URL 缺少填充
    ]
    
    def safe_base64_decode(data):
        """安全的 base64 解码函数"""
        try:
            if not data or not data.strip():
                return None, "空数据"
            
            clean_data = data.strip()
            
            # 处理 data URL
            if clean_data.startswith('data:'):
                if ',' in clean_data:
                    clean_data = clean_data.split(',', 1)[1]
                else:
                    return None, "无效的 data URL 格式"
            
            # 修复填充
            missing_padding = len(clean_data) % 4
            if missing_padding:
                clean_data += '=' * (4 - missing_padding)
            
            # 解码
            decoded = base64.b64decode(clean_data)
            return decoded, "成功"
            
        except Exception as e:
            return None, str(e)
    
    success_count = 0
    for i, test_case in enumerate(test_cases):
        print(f"测试用例 {i+1}: '{test_case}'")
        result, message = safe_base64_decode(test_case)
        if result is not None:
            print(f"  ✅ 成功: {message}, 解码长度: {len(result)}")
            success_count += 1
        else:
            print(f"  ⚠️  失败: {message}")
    
    print(f"\n边界测试结果: {success_count}/{len(test_cases)} 个用例处理正确")
    return success_count > 0

def main():
    """主测试函数"""
    print("开始测试 base64 解码修复功能...\n")
    
    test1 = test_base64_padding_fix()
    test2 = test_data_url_handling()
    test3 = test_edge_cases()
    
    print(f"\n总体测试结果:")
    print(f"填充修复测试: {'✅ 通过' if test1 else '❌ 失败'}")
    print(f"Data URL 处理测试: {'✅ 通过' if test2 else '❌ 失败'}")
    print(f"边界情况测试: {'✅ 通过' if test3 else '❌ 失败'}")
    
    if test1 and test2 and test3:
        print("\n🎉 所有测试通过！base64 解码修复功能正常工作。")
    else:
        print("\n⚠️  部分测试失败，需要进一步检查。")

if __name__ == "__main__":
    main()
